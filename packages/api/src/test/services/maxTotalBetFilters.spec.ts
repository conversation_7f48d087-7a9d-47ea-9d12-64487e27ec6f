import { suite, test } from "mocha-typescript";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { EntityGame } from "../../skywind/entities/game";
import { findPlayerLimits } from "../../skywind/services/limits";
import { SinonFakeTimers, SinonStub, stub, useFakeTimers } from "sinon";
import * as gameLimitsCurrenciesCache from "../../skywind/cache/gameLimitsCurrencies";
import * as EntityJurisdictionCache from "../../skywind/cache/entityJurisdiction";
import * as currencyExchangeService from "../../skywind/services/currencyExchange";
import { buildDynamicMaxTotalStake } from "../../skywind/services/gameLimits/jurisdictionFilter";
import { SlotGameLimits } from "../../skywind/entities/gamegroup";

should();
use(chaiAsPromised);

@suite()
class MaxTotalBetFiltersSpec {
    public static entityGame: EntityGame;
    public clock: SinonFakeTimers;
    public static gameLimitsCurrenciesCacheStub: SinonStub;
    public static currencyExchangeStub: SinonStub;

    public static async before() {
        await truncate();
        await gameLimitsCurrenciesCache.reset();
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub = stub(gameLimitsCurrenciesCache, "getGameLimitsCurrency");
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.resolves({});

        // Mock currency exchange service
        const mockCurrencyExchange = {
            exchange: (amount: number, fromCurrency: string, toCurrency: string) => {
                // Simple mock exchange rates for testing
                const rates = {
                    "EUR": { "USD": 1.1, "GBP": 0.85, "EUR": 1 },
                    "USD": { "EUR": 0.91, "GBP": 0.77, "USD": 1 },
                    "GBP": { "EUR": 1.18, "USD": 1.3, "GBP": 1 }
                };
                return amount * (rates[fromCurrency]?.[toCurrency] || 1);
            }
        };
        MaxTotalBetFiltersSpec.currencyExchangeStub = stub(currencyExchangeService, "getCurrencyExchange");
        MaxTotalBetFiltersSpec.currencyExchangeStub.resolves(mockCurrencyExchange);
    }

    public static after() {
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.restore();
        MaxTotalBetFiltersSpec.currencyExchangeStub.restore();
    }

    public before() {
        this.clock = useFakeTimers();
        this.clock.setSystemTime(0);
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsNotLimited() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsLimitedByOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeaturesIsLimitedByRegulation() {
        EntityJurisdictionCache.reset();
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 25
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            undefined,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 45,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 30,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    // ========== skipJurisdictionFiltering Tests ==========

    @test("skipJurisdictionFiltering: should return undefined when skipJurisdictionFiltering is true")
    public async testSkipJurisdictionFilteringReturnsUndefined() {
        const brand = await factory.create(FACTORY.BRAND);

        const result = await buildDynamicMaxTotalStake(brand, "EUR", true, 100);

        expect(result).to.be.undefined;
    }

    @test("skipJurisdictionFiltering: should process jurisdiction when skipJurisdictionFiltering is false")
    public async testSkipJurisdictionFilteringProcessesJurisdiction() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 50,
                    dynamicMaxTotalBetLimitEnabled: false
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 100);

        expect(result).to.equal(50);
    }

    @test("skipJurisdictionFiltering: should return undefined when no jurisdiction exists")
    public async testSkipJurisdictionFilteringNoJurisdiction() {
        const brand = await factory.create(FACTORY.BRAND);

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 100);

        expect(result).to.be.undefined;
    }

    @test("skipJurisdictionFiltering: should bypass jurisdiction filtering in findPlayerLimits")
    public async testSkipJurisdictionFilteringInFindPlayerLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });

        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        // Create jurisdiction with restrictive maxTotalStake
        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 20  // Very restrictive
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            true);  // skipJurisdictionFiltering parameter

        // Should use game's maxTotalStake (100) instead of jurisdiction's (20)
        expect((result as SlotGameLimits).maxTotalStake).to.equal(100);
    }

    // ========== operatorDynamicMaxTotalBetLimit Tests ==========

    @test("operatorDynamicMaxTotalBetLimit: should return operator limit when dynamic feature disabled")
    public async testOperatorLimitWhenDynamicFeatureDisabled() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 50,
                    dynamicMaxTotalBetLimitEnabled: false
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 100);

        // Should return maxTotalStake since dynamic feature is disabled
        expect(result).to.equal(50);
    }

    @test("operatorDynamicMaxTotalBetLimit: should return operator limit when no jurisdiction defaults")
    public async testOperatorLimitWhenNoJurisdictionDefaults() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        // No defaultTotalBet or defaultMaxTotalBet
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 100);

        // Should return operator limit when jurisdiction defaults are invalid
        expect(result).to.equal(100);
    }

    @test("operatorDynamicMaxTotalBetLimit: should return jurisdiction default when no operator limit")
    public async testJurisdictionDefaultWhenNoOperatorLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 75,
                        defaultMaxTotalBet: 150,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false);

        // Should return jurisdiction defaultTotalBet when no operator limit provided
        expect(result).to.equal(75);
    }

    @test("operatorDynamicMaxTotalBetLimit: should return operator limit when within jurisdiction max")
    public async testOperatorLimitWithinJurisdictionMax() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 150,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 100);

        // Operator limit (100) is within jurisdiction max (150), so return operator limit
        expect(result).to.equal(100);
    }

    @test("operatorDynamicMaxTotalBetLimit: should return jurisdiction max when operator limit exceeds it")
    public async testJurisdictionMaxWhenOperatorLimitExceeds() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 120,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 200);

        // Operator limit (200) exceeds jurisdiction max (120), so return jurisdiction max
        expect(result).to.equal(120);
    }

    @test("operatorDynamicMaxTotalBetLimit: should return operator limit when no jurisdiction max")
    public async testOperatorLimitWhenNoJurisdictionMax() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        // No defaultMaxTotalBet
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 200);

        // No jurisdiction max limit, so return operator limit
        expect(result).to.equal(200);
    }

    @test("operatorDynamicMaxTotalBetLimit: should handle currency conversion correctly")
    public async testCurrencyConversionInDynamicLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 100,
                        defaultMaxTotalBet: 200,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "USD", false, 150);

        // Operator limit (150 USD) should be compared against jurisdiction max (200 EUR * 1.1 = 220 USD)
        // Since 150 < 220, should return operator limit
        expect(result).to.equal(150);
    }

    @test("operatorDynamicMaxTotalBetLimit: should handle currency conversion when operator exceeds max")
    public async testCurrencyConversionWhenOperatorExceedsMax() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 100,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "USD", false, 150);

        // Operator limit (150 USD) vs jurisdiction max (100 EUR * 1.1 = 110 USD)
        // Since 150 > 110, should return jurisdiction max converted to USD
        expect(result).to.be.closeTo(110, 0.01);
    }

    @test("operatorDynamicMaxTotalBetLimit: should use default currency when jurisdiction currency not specified")
    public async testDefaultCurrencyWhenNotSpecified() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 75,
                        defaultMaxTotalBet: 150
                        // No currency specified, should default to EUR
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "USD", false);

        // Should use EUR as default currency and convert 75 EUR to USD (75 * 1.1 = 82.5)
        expect(result).to.equal(82.5);
    }

    @test("operatorDynamicMaxTotalBetLimit: should fallback to maxTotalStake when dynamic feature enabled but no valid config")
    public async testFallbackToMaxTotalStakeWhenInvalidDynamicConfig() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 60,
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        // Invalid config - missing both defaultTotalBet and defaultMaxTotalBet
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false);

        // Should fallback to maxTotalStake when dynamic config is invalid and no operator limit
        expect(result).to.equal(60);
    }

    // ========== Edge Cases and Error Handling ==========

    @test("Edge case: should handle zero operator limit")
    public async testZeroOperatorLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 100,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 0);

        // Should return jurisdiction default when operator limit is 0
        expect(result).to.equal(50);
    }

    @test("Edge case: should handle negative operator limit")
    public async testNegativeOperatorLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 100,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, -10);

        // Should return the negative operator limit as the function doesn't validate input
        expect(result).to.equal(-10);
    }

    @test("Edge case: should handle very large operator limit")
    public async testVeryLargeOperatorLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 100,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, 999999);

        // Should cap at jurisdiction max when operator limit is very large
        expect(result).to.equal(100);
    }

    @test("Edge case: should handle undefined operator limit with valid jurisdiction defaults")
    public async testUndefinedOperatorLimitWithValidDefaults() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 75,
                        defaultMaxTotalBet: 150,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, undefined);

        // Should return jurisdiction default when operator limit is undefined
        expect(result).to.equal(75);
    }

    @test("Edge case: should handle null operator limit")
    public async testNullOperatorLimit() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 80,
                        defaultMaxTotalBet: 160,
                        currency: "EUR"
                    }
                }
            }
        });

        const result = await buildDynamicMaxTotalStake(brand, "EUR", false, null);

        // Should return jurisdiction default when operator limit is null
        expect(result).to.equal(80);
    }

    // ========== Integration Tests ==========

    @test("Integration: should work correctly with findPlayerLimits and dynamic limits enabled")
    public async testIntegrationWithFindPlayerLimitsAndDynamicLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 10, 15, 20],
                        stakeDef: 2,
                        stakeMax: 20,
                        stakeMin: 1,
                        maxTotalStake: 200
                    }
                }
            }
        });

        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        // Create jurisdiction with dynamic limits enabled
        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 50,
                        defaultMaxTotalBet: 100,
                        currency: "EUR"
                    }
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,  // skipJurisdictionFiltering
            80);    // operatorDynamicMaxTotalBetLimit

        // Should use operator limit (80) since it's within jurisdiction max (100)
        expect((result as SlotGameLimits).maxTotalStake).to.equal(80);
        // Stakes should be limited by the dynamic max total stake
        expect((result as SlotGameLimits).stakeAll).to.deep.equal([1, 2, 3, 5]);
        expect((result as SlotGameLimits).stakeMax).to.equal(5);
    }

    @test("Integration: should work correctly when operator limit exceeds jurisdiction max")
    public async testIntegrationWhenOperatorExceedsJurisdictionMax() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 10, 15, 20],
                        stakeDef: 2,
                        stakeMax: 20,
                        stakeMin: 1,
                        maxTotalStake: 200
                    }
                }
            }
        });

        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        // Create jurisdiction with dynamic limits enabled
        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 30,
                        defaultMaxTotalBet: 60,
                        currency: "EUR"
                    }
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,  // skipJurisdictionFiltering
            100);   // operatorDynamicMaxTotalBetLimit - Exceeds jurisdiction max of 60

        // Should use jurisdiction max (60) since operator limit (100) exceeds it
        expect((result as SlotGameLimits).maxTotalStake).to.equal(60);
        // Stakes should be limited by the jurisdiction max
        expect((result as SlotGameLimits).stakeAll).to.deep.equal([1, 2, 3, 5]);
        expect((result as SlotGameLimits).stakeMax).to.equal(5);
    }

    @test("Integration: should combine skipJurisdictionFiltering with operatorDynamicMaxTotalBetLimit correctly")
    public async testIntegrationSkipJurisdictionWithOperatorLimit() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 10, 15, 20],
                        stakeDef: 2,
                        stakeMax: 20,
                        stakeMin: 1,
                        maxTotalStake: 200
                    }
                }
            }
        });

        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        // Create jurisdiction with very restrictive limits
        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 10,  // Very restrictive
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 5,
                        defaultMaxTotalBet: 15,
                        currency: "EUR"
                    }
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            true,   // skipJurisdictionFiltering - Skip jurisdiction filtering
            100);   // operatorDynamicMaxTotalBetLimit

        // Should use game's original maxTotalStake (200) since jurisdiction filtering is skipped
        expect((result as SlotGameLimits).maxTotalStake).to.equal(200);
        // Stakes should not be limited by jurisdiction
        expect((result as SlotGameLimits).stakeAll).to.deep.equal([1, 2, 3, 5, 10, 15, 20]);
        expect((result as SlotGameLimits).stakeMax).to.equal(20);
    }

    @test("Complex scenario: multiple currencies with dynamic limits")
    public async testComplexScenarioMultipleCurrencies() {
        const brand = await factory.create(FACTORY.BRAND);

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    dynamicMaxTotalBetLimit: {
                        defaultTotalBet: 100,
                        defaultMaxTotalBet: 200,
                        currency: "EUR"
                    }
                }
            }
        });

        // Test USD conversion
        const resultUSD = await buildDynamicMaxTotalStake(brand, "USD", false, 180);
        // 180 USD vs 200 EUR * 1.1 = 220 USD, so should return 180
        expect(resultUSD).to.equal(180);

        // Test GBP conversion
        const resultGBP = await buildDynamicMaxTotalStake(brand, "GBP", false, 180);
        // 180 GBP vs 200 EUR * 0.85 = 170 GBP, so should return 170 (jurisdiction max)
        expect(resultGBP).to.equal(170);
    }

}
